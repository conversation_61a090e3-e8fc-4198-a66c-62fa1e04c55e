import { View, Text } from '@tarojs/components';
import { useEffect, useState } from 'react';
import Taro from '@tarojs/taro';
import PageSwipeBack from '@/components/PageSwipeBack';
import YkNavBar from '@/components/ykNavBar';
import { Toast, Button, Avatar, Badge, Image } from '@arco-design/mobile-react';
import { getUserInfo } from '@/utils/api/common/common_user';

import {
  VipType,
  VipPackage,
  UserInfo
} from './types';
import { useVipPayment } from '@/utils/useVipPayment';
import {
  isDebugMode,
  getDebugScenario,
  debugLog,
  generateMockUserData,
  formatPrice,
  formatMemberExpireTime,
  isMemberValid,
  vipCenterDebugger
} from './utils';
import { DebugPanel, DebugButton } from '@/pages/debug';
import BottomPopup from '@/components/BottomPopup';
import './index.less';

// 前端配置的会员套餐数据
const VIP_PACKAGES: VipPackage[] = [
  {
    type: VipType.HALF_YEAR,
    name: '半年会员',
    price: 16800, // 168元，以分为单位
    description: '享受6个月会员特权',
    recommended: false
  },
  {
    type: VipType.ANNUAL,
    name: '年度会员',
    price: 28800, // 288元，以分为单位
    description: '享受12个月会员特权，更优惠',
    recommended: true
  }
];

const VipCenter = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [paymentPopupVisible, setPaymentPopupVisible] = useState(false);
  const [selectedVipType, setSelectedVipType] = useState<VipType | null>(null);

  // 支付成功回调
  const handlePaymentSuccess = async (paymentData: any) => {
    console.log('[VIP Payment] 🎉 支付成功回调开始', {
      paymentData,
      timestamp: new Date().toISOString()
    });
    debugLog('[payment] 支付成功', paymentData);

    try {
      // 1. 设置刷新标记，用于从支付成功页面返回时刷新
      console.log('[VIP Payment] 🏷️ 设置支付成功刷新标记');
      Taro.setStorageSync('vip_payment_success_refresh', true);

      // 2. 先跳转到支付成功页面，让用户看到成功状态
      console.log('[VIP Payment] 🔄 立即跳转到支付成功页面');
      Taro.navigateTo({
        url: `/pages/vipCenter/paySuccess?orderId=${paymentData?.payOrderId || ''}&vipType=${paymentData?.vipType || ''}`
      });

      // 3. 显示成功提示
      Toast.success('支付成功');

      // 4. 后台延迟等待后端数据处理完成，然后刷新用户信息
      console.log('[VIP Payment] ⏰ 后台等待后端数据处理完成...');
      setTimeout(async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒
          console.log('[VIP Payment] 📡 开始后台延迟刷新用户信息...');
          await forceRefreshUserInfo();
          console.log('[VIP Payment] ✅ 后台延迟用户信息刷新完成');
        } catch (error) {
          console.error('[VIP Payment] ❌ 后台延迟刷新失败', error);
        }
      }, 0);

      console.log('[VIP Payment] 🎉 支付成功处理完成');
    } catch (error) {
      console.error('[VIP Payment] ❌ 支付成功回调处理失败', error);
      // 即使失败也要设置刷新标记和跳转
      Taro.setStorageSync('vip_payment_success_refresh', true);
      Taro.navigateTo({
        url: `/pages/vipCenter/paySuccess?orderId=${paymentData?.payOrderId || ''}&vipType=${paymentData?.vipType || ''}`
      });
      Toast.success('支付成功');
    }
  };

  // 支付失败回调
  const handlePaymentFailure = (errorMessage: string) => {
    debugLog('[payment] 支付失败', errorMessage);
    Toast.error(errorMessage || '支付失败');
  };

  // 支付取消回调
  const handlePaymentCancel = () => {
    debugLog('[payment] 支付取消');
    Toast.info('支付已取消');
  };

  // 使用支付Hook
  const {
    isPaying,
    error: paymentError,
    paymentStep,
    initiateVipPayment,
    resetPaymentState
  } = useVipPayment({
    onSuccess: handlePaymentSuccess,
    onFailure: handlePaymentFailure,
    onCancel: handlePaymentCancel,
    debug: isDebugMode()
  });

  // 强制刷新远程用户信息并更新本地存储（用于支付成功后）
  const forceRefreshUserInfo = async (): Promise<void> => {
    console.log('[Force Refresh] 🔄 开始强制刷新用户信息');

    try {
      // 调试模式处理
      if (isDebugMode()) {
        const scenario = getDebugScenario();
        console.log('[Force Refresh] 🐛 调试模式，场景:', scenario);
        debugLog(`[basic] 检测到调试模式，场景: ${scenario}`);

        const mockUser = generateMockUserData(scenario);
        setUserInfo(mockUser);
        debugLog('[mock] 使用模拟用户数据', mockUser);
        return Promise.resolve();
      }

      // 获取本地用户信息作为基础
      const localUserInfo = Taro.getStorageSync("userInfo");
      console.log('[Force Refresh] 📱 本地用户信息:', localUserInfo);

      if (!localUserInfo) {
        console.warn('[Force Refresh] ⚠️ 本地用户信息为空，无法刷新');
        throw new Error('本地用户信息为空');
      }

      // 强制从远程获取最新用户信息
      console.log('[Force Refresh] 📡 开始请求远程用户信息...');
      const res = await getUserInfo();
      console.log('[Force Refresh] 📡 远程用户信息响应:', res);

      if (res.code === 0) {
        // 合并本地信息和远程信息，远程信息优先
        const updatedUserInfo = {
          ...localUserInfo,
          ...res.data,
        };

        console.log('[Force Refresh] 🔄 合并后的用户信息:', updatedUserInfo);

        // 更新本地存储
        Taro.setStorageSync("userInfo", updatedUserInfo);
        console.log('[Force Refresh] 💾 已更新本地存储');

        // 更新组件状态
        setUserInfo(updatedUserInfo);
        console.log('[Force Refresh] ✅ 用户信息强制刷新成功');

        debugLog('[force-refresh] 用户信息强制刷新成功', updatedUserInfo);
      } else {
        console.error('[Force Refresh] ❌ 远程用户信息获取失败:', res);
        throw new Error(`远程用户信息获取失败: ${res.msg || '未知错误'}`);
      }
    } catch (error) {
      console.error('[Force Refresh] ❌ 强制刷新用户信息失败:', error);
      debugLog('[force-refresh] 强制刷新用户信息失败', error);
      throw error;
    }
  };

  // 获取用户信息（从本地存储）
  const fetchUserInfo = () => {
    try {
      debugLog('[basic] 开始获取本地用户信息');

      // 调试模式处理
      if (isDebugMode()) {
        const scenario = getDebugScenario();
        debugLog(`[basic] 检测到调试模式，场景: ${scenario}`);

        const mockUser = generateMockUserData(scenario);
        setUserInfo(mockUser);
        debugLog('[mock] 使用模拟用户数据', mockUser);
        return;
      }

      // 从本地存储获取用户信息
      const localUserInfo = Taro.getStorageSync("userInfo");
      debugLog('获取本地用户信息', localUserInfo);
      if (localUserInfo) {
        //获取远程用户信息覆盖本地数据（非阻塞）
        getUserInfo().then((res: any) => {
          if (res.code == 0) {
            //更新用户信息
            const updatedUserInfo = {
              ...localUserInfo,
              ...res.data,
            };
            Taro.setStorageSync("userInfo", updatedUserInfo);
            setUserInfo(updatedUserInfo);
            console.log('[Fetch User] 📡 后台更新用户信息成功:', updatedUserInfo);
          }
        }).catch((error) => {
          console.warn('[Fetch User] ⚠️ 后台更新用户信息失败:', error);
        });

        // 先使用本地信息
        setUserInfo(localUserInfo);
        debugLog('本地用户信息加载成功', localUserInfo);
      } else {
        debugLog('本地用户信息为空');
        Toast.info('请先登录');
      }
    } catch (error) {
      debugLog('获取本地用户信息失败', error);
      Toast.error('获取用户信息失败');
    }
  };

  // 判断用户是否为会员（使用工具函数）
  const isUserVip = () => {
    if (!userInfo) return false;

    debugLog('[basic] 检查会员状态', {
      isMember: userInfo.isMember,
      memberExpireTime: userInfo.memberExpireTime,
      type: typeof userInfo.memberExpireTime
    });

    // 检查会员状态和过期时间
    if (userInfo.isMember === 1 && userInfo.memberExpireTime) {
      const isValid = isMemberValid(userInfo.memberExpireTime);

      debugLog('[basic] 会员有效性检查', {
        expireTime: userInfo.memberExpireTime,
        isValid,
        now: new Date().toISOString()
      });

      return isValid;
    }

    return false;
  };

  // 格式化会员到期时间（使用工具函数）
  const formatExpireTime = () => {
    if (!userInfo || !userInfo.memberExpireTime) return '';

    debugLog('[format] 原始到期时间数据', {
      expireTime: userInfo.memberExpireTime,
      type: typeof userInfo.memberExpireTime
    });

    const formatted = formatMemberExpireTime(userInfo.memberExpireTime);

    debugLog('[format] 格式化完成', {
      original: userInfo.memberExpireTime,
      formatted
    });

    return formatted;
  };

  // 处理套餐购买 - 显示支付方式选择
  const handlePurchase = (vipType: VipType) => {
    debugLog('[purchase] 开始购买流程', { vipType });

    // 打印所有 window 注入的对象、对象和事件名称
    console.group('🔍 [Window Objects] 检查所有注入的对象和事件');

    // 使用类型断言来访问动态注入的属性
    const windowAny = window as any;

    // 1. 检查支付相关的对象
    console.group('💳 支付相关对象');
    console.log('window.wxPay:', windowAny.wxPay);
    console.log('window.wxPay?.wxPay:', windowAny.wxPay?.wxPay);
    console.log('window.aliPay:', windowAny.aliPay);
    console.log('window.aliPay?.aliPay:', windowAny.aliPay?.aliPay);
    console.groupEnd();

    // 2. 检查 WebKit 相关对象
    console.group('📱 WebKit 相关对象');
    console.log('window.webkit:', windowAny.webkit);
    console.log('window.webkit?.messageHandlers:', windowAny.webkit?.messageHandlers);
    if (windowAny.webkit?.messageHandlers) {
      console.log('messageHandlers keys:', Object.keys(windowAny.webkit.messageHandlers));
      Object.keys(windowAny.webkit.messageHandlers).forEach(key => {
        console.log(`messageHandlers.${key}:`, windowAny.webkit.messageHandlers[key]);
      });
    }
    console.groupEnd();

    // 3. 检查所有自定义的 window 属性（过滤掉原生属性）
    console.group('🔧 自定义 Window 属性');
    const customProps: Array<{name: string, type: string, value: any}> = [];
    const nativeProps = new Set([
      'window', 'self', 'document', 'name', 'location', 'history', 'customElements',
      'locationbar', 'menubar', 'personalbar', 'scrollbars', 'statusbar', 'toolbar',
      'status', 'closed', 'frames', 'length', 'top', 'opener', 'parent', 'frameElement',
      'navigator', 'applicationCache', 'screen', 'innerHeight', 'innerWidth',
      'scrollX', 'pageXOffset', 'scrollY', 'pageYOffset', 'screenX', 'screenY',
      'outerHeight', 'outerWidth', 'devicePixelRatio', 'clientInformation',
      'screenLeft', 'screenTop', 'defaultStatus', 'defaultstatus', 'styleMedia',
      'onsearch', 'isSecureContext', 'trustedTypes', 'performance', 'onappinstalled',
      'onbeforeinstallprompt', 'crypto', 'indexedDB', 'webkitStorageInfo', 'sessionStorage',
      'localStorage', 'console', 'onpointerrawupdate', 'speechSynthesis', 'webkitRequestFileSystem',
      'webkitResolveLocalFileSystemURL', 'openDatabase'
    ]);

    for (const prop in windowAny) {
      if (!nativeProps.has(prop) && typeof windowAny[prop] !== 'function') {
        customProps.push({
          name: prop,
          type: typeof windowAny[prop],
          value: windowAny[prop]
        });
      }
    }

    console.log('自定义属性列表:', customProps);
    customProps.forEach(prop => {
      console.log(`${prop.name} (${prop.type}):`, prop.value);
    });
    console.groupEnd();

    // 4. 检查所有自定义的 window 函数
    console.group('⚡ 自定义 Window 函数');
    const customFunctions: Array<{name: string, func: any}> = [];
    for (const prop in windowAny) {
      if (!nativeProps.has(prop) && typeof windowAny[prop] === 'function') {
        customFunctions.push({
          name: prop,
          func: windowAny[prop]
        });
      }
    }

    console.log('自定义函数列表:', customFunctions.map(f => f.name));
    customFunctions.forEach(func => {
      console.log(`${func.name}():`, func.func);
    });
    console.groupEnd();

    // 5. 检查事件监听器相关
    console.group('🎯 事件相关');
    const eventProps: Array<{name: string, handler: any}> = [];
    for (const prop in windowAny) {
      if (prop.startsWith('on') && typeof windowAny[prop] === 'function') {
        eventProps.push({
          name: prop,
          handler: windowAny[prop]
        });
      }
    }
    console.log('事件处理器:', eventProps.map(e => e.name));
    eventProps.forEach(event => {
      console.log(`${event.name}:`, event.handler);
    });
    console.groupEnd();

    // 6. 检查 Taro 相关对象
    console.group('🌟 Taro 相关对象');
    console.log('window.Taro:', windowAny.Taro);
    console.log('window.__taroAppConfig:', windowAny.__taroAppConfig);
    console.log('window.__TARO_ENV:', windowAny.__TARO_ENV);
    console.groupEnd();

    // 7. 检查其他可能的注入对象
    console.group('🔍 其他可能的注入对象');
    const possibleInjections = [
      'Android', 'iOS', 'NativeApp', 'JSBridge', 'bridge',
      'paySuccess', 'payFailure', 'webPaySuc', 'aliPayWithStr', 'wxPayWithStr',
      'iosPaySuccess', 'iosPayFailure', 'iosRestoreSuccess',
      'customerService', 'share', 'camera', 'location'
    ];

    possibleInjections.forEach(name => {
      if (windowAny[name] !== undefined) {
        console.log(`${name}:`, windowAny[name]);
      }
    });
    console.groupEnd();

    console.groupEnd(); // 结束主分组

    if (isDebugMode()) {
      Toast.success('调试模式：购买成功');
      return Promise.resolve();
    }

    // 保存选中的套餐类型并显示支付方式选择弹窗
    setSelectedVipType(vipType);
    setPaymentPopupVisible(true);
  };

  // 执行实际支付流程（使用useVipPayment Hook）
  const executePayment = (vipType: VipType, channelCode: string) => {
    debugLog('[payment] 开始执行支付', { vipType, channelCode });

    // 获取套餐信息
    const selectedPackage = VIP_PACKAGES.find(pkg => pkg.type === vipType);
    if (!selectedPackage) {
      Toast.error('套餐信息不存在');
      return;
    }

    // 获取用户信息
    const localUserInfo = Taro.getStorageSync("userInfo");
    if (!localUserInfo || !localUserInfo.id) {
      Toast.error('请先登录');
      return;
    }

    // 使用Hook发起支付，传递channelCode参数
    debugLog('[payment] 使用useVipPayment Hook发起支付', {
      vipType,
      price: selectedPackage.price,
      channelCode
    });

    initiateVipPayment(vipType, selectedPackage.price, channelCode);
  };

  // 获取支付调试按钮配置
  const getPaymentDebugButtons = (): DebugButton[] => {
    return [
      // 基础调试按钮
      { label: '🔄 基础调试', scenario: 'basic' },
      { label: '📡 API调试', scenario: 'api' },
      { label: '🎭 模拟数据', scenario: 'mock' },
      { label: '❌ 错误处理', scenario: 'error' },
      { label: '⏳ 加载状态', scenario: 'loading' },
      { label: '📭 空状态', scenario: 'empty' },

      // 支付调试按钮
      { label: '🔍 检测环境', scenario: 'detect_env', color: 'primary' },
      { label: '💳 测试微信支付', scenario: 'test_wechat', color: 'success' },
      { label: '💰 测试支付宝', scenario: 'test_alipay', color: 'success' },
      { label: '📱 查看UA', scenario: 'show_ua', color: 'warning' },
      { label: '⏰ 测试时间戳', scenario: 'test_timestamp', color: 'warning' },
    ];
  };

  // 处理支付方式选择
  const handlePaymentMethodSelect = (index: number) => {
    if (!selectedVipType) {
      Toast.error('请先选择套餐');
      return;
    }

    const paymentMethods = [
      { name: '微信支付', channelCode: 'wx_app' },
      { name: '支付宝', channelCode: 'alipay_app' }
    ];

    const selectedMethod = paymentMethods[index];
    if (!selectedMethod) {
      Toast.error('支付方式选择错误');
      return;
    }

    debugLog('[payment] 选择支付方式', {
      method: selectedMethod.name,
      channelCode: selectedMethod.channelCode,
      vipType: selectedVipType
    });

    // 关闭弹窗并执行支付
    setPaymentPopupVisible(false);
    executePayment(selectedVipType, selectedMethod.channelCode);
  };

  // 关闭支付方式选择弹窗
  const handlePaymentPopupClose = () => {
    setPaymentPopupVisible(false);
    setSelectedVipType(null);
  };

  // 跳转到会员权益页面
  const goToBenefits = () => {
    Toast.info('会员权益页面开发中');
  };

  // 已购服务
  const goToPurchasedServices = () => {
    if(isDebugMode()) {
      Taro.navigateTo({ url: '/pages/vipCenter/payOrder?debug' });
      return
    }

    Taro.navigateTo({ url: '/pages/vipCenter/payOrder' });
  };

  // 恢复购买
  const restorePurchase = () => {
    Toast.info('恢复购买功能开发中');
  };

  // 支付调试处理函数
  const handleDetectEnvironment = () => {
    import('@/utils/paymentService').then((module) => {
      try {
        module.getPaymentEnvironment();
      } catch (e) {
        console.log('环境检测完成，请查看控制台');
      }
      Taro.showToast({
        title: '环境检测完成，请查看控制台',
        icon: 'success',
        duration: 2000
      });
    });
  };

  const handleTestWechatPay = () => {
    const testParams = {
      appid: "test_appid",
      partnerId: "test_partner",
      prepayId: "test_prepay",
      packageValue: "Sign=WXPay",
      noncestr: "test_nonce",
      timestamp: "test_timestamp",
      sign: "test_sign"
    };

    const wxPay = (window as any).wxPay;
    if (wxPay && wxPay.wxPay) {
      console.log('🔍 [TEST] 测试微信支付调用');
      wxPay.wxPay(JSON.stringify(testParams));
      Taro.showToast({ title: '微信支付调用完成', icon: 'success' });
    } else {
      Taro.showModal({
        title: '测试结果',
        content: '微信支付功能不可用\n' +
                `window.wxPay: ${!!wxPay}\n` +
                `window.wxPay.wxPay: ${!!(wxPay && wxPay.wxPay)}`,
        showCancel: false
      });
    }
  };

  const handleTestAlipay = () => {
    const testPayInfo = "app_id=test&method=alipay.trade.app.pay&charset=utf-8&sign_type=RSA2&timestamp=test&version=1.0";

    const aliPay = (window as any).aliPay;
    if (aliPay && aliPay.aliPay) {
      console.log('🔍 [TEST] 测试支付宝支付调用');
      aliPay.aliPay(testPayInfo);
      Taro.showToast({ title: '支付宝支付调用完成', icon: 'success' });
    } else {
      Taro.showModal({
        title: '测试结果',
        content: '支付宝支付功能不可用\n' +
                `window.aliPay: ${!!aliPay}\n` +
                `window.aliPay.aliPay: ${!!(aliPay && aliPay.aliPay)}`,
        showCancel: false
      });
    }
  };

  const handleShowUserAgent = () => {
    const ua = window.navigator.userAgent;
    Taro.showModal({
      title: 'UserAgent',
      content: ua,
      showCancel: false
    });
  };

  const handleTestTimestamp = () => {
    const testTimestamps = [
      Date.now() + 86400000,        // 明天（毫秒级）
      Math.floor(Date.now() / 1000) + 86400, // 明天（秒级）
      1735689600000,                // 2025-01-01（毫秒级）
      1735689600,                   // 2025-01-01（秒级）
      "1735689600000",              // 毫秒级字符串
      "2025-12-31",                 // 日期字符串
    ];

    let results = '时间戳测试结果:\n\n';
    testTimestamps.forEach((timestamp, index) => {
      const formatted = formatMemberExpireTime(timestamp);
      const isValid = isMemberValid(timestamp);
      results += `${index + 1}. ${timestamp}\n`;
      results += `   格式化: ${formatted}\n`;
      results += `   有效: ${isValid ? '✅' : '❌'}\n\n`;
    });

    Taro.showModal({
      title: '时间戳测试',
      content: results,
      showCancel: false
    });
  };

  // 加载模拟数据
  const loadMockData = async (scenario: string) => {
    debugLog(`[mock] 开始加载模拟数据，场景: ${scenario}`);

    // 处理支付调试场景
    switch (scenario) {
      case 'detect_env':
        handleDetectEnvironment();
        return;
      case 'test_wechat':
        handleTestWechatPay();
        return;
      case 'test_alipay':
        handleTestAlipay();
        return;
      case 'show_ua':
        handleShowUserAgent();
        return;
      case 'test_timestamp':
        handleTestTimestamp();
        return;
    }

    const mockUser = generateMockUserData(scenario);
    setUserInfo(mockUser);
    debugLog(`[mock] 模拟数据加载完成`, mockUser);
  };

  // 页面显示时的处理
  const handlePageShow = async () => {
    console.log('[VIP Center] 📱 页面显示事件触发');

    // 检查是否从支付成功页面返回
    const pages = Taro.getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const prevPage = pages.length > 1 ? pages[pages.length - 2] : null;

    console.log('[VIP Center] 📄 页面信息', {
      currentRoute: currentPage?.route,
      prevRoute: prevPage?.route,
      pagesLength: pages.length
    });

    // 如果是从支付成功页面返回，强制刷新用户信息
    if (prevPage?.route?.includes('vipCenter/paySuccess')) {
      console.log('[VIP Center] 🔄 检测到从支付成功页面返回，强制刷新用户信息');
      try {
        await forceRefreshUserInfo();
        console.log('[VIP Center] ✅ 返回后用户信息刷新完成');
        // Toast.success('会员信息已更新');
      } catch (error) {
        console.error('[VIP Center] ❌ 返回后用户信息刷新失败', error);
        // 失败时使用普通刷新
        fetchUserInfo();
      }
    } else {
      // 正常页面加载，使用普通刷新
      console.log('[VIP Center] 📡 正常页面加载，使用普通刷新');
      fetchUserInfo();
    }
  };

  useEffect(() => {
    handlePageShow();
  }, []);

  // 使用 Taro 页面生命周期监听页面显示
  Taro.useDidShow(() => {
    console.log('[VIP Center] 👁️ 页面显示生命周期触发');

    // 检查是否需要强制刷新用户信息
    // 这里可以通过全局状态或本地存储标记来判断是否需要刷新
    const needRefresh = Taro.getStorageSync('vip_payment_success_refresh');

    if (needRefresh) {
      console.log('[VIP Center] 🔄 检测到支付成功标记，强制刷新用户信息');

      // 清除标记
      Taro.removeStorageSync('vip_payment_success_refresh');

      // 延迟一下确保页面完全显示
      setTimeout(async () => {
        try {
          await forceRefreshUserInfo();
          console.log('[VIP Center] ✅ 支付成功后用户信息刷新完成');
          // Toast.success('会员信息已更新');
        } catch (error) {
          console.error('[VIP Center] ❌ 支付成功后用户信息刷新失败', error);
          // 失败时使用普通刷新
          fetchUserInfo();
        }
      }, 500);
    }
  });

  return (
    <PageSwipeBack>
      <View className="vip-center">
        {/* 导航栏 */}
        <YkNavBar title="会员中心" />

        {/* 用户信息区域 */}
        <View className="user-info-section">
          {/* <View className="user-avatar"> */}
            {/* <Image
              src={userInfo?.avatar || ""}
              className="avatar-image"
              radius="50%"
            /> */}
            <Avatar
              src={userInfo?.avatar || ""}
              className="avatar-image"
              size="large"
            />
          {/* </View> */}
          <Text className="user-name">
            {userInfo?.nickname || "梦想潮鞋货源批发…"}
          </Text>
          {isUserVip() && (
            <View className="member-expire-info">
              <Text className="member-expire-msg">
                会员到期日：
              </Text>
              <Text className="member-expire-time">
                {formatExpireTime()}
              </Text>
            </View>
          )}
        </View>

        {/* 套餐选择区域 */}
        <View className="packages-section">
          {VIP_PACKAGES.map((pkg) => (
            <View key={pkg.type} className="package-card">
              <View className="package-content">
                <View className="package-left">
                  <View className="vip-box">
                    <View className="vip-badge">
                      <Text className="vip-text">VIP</Text>
                    </View>
                  </View>
                  <View className="package-info">
                    <View className="package-name-container">
                      <Text className="package-name">{pkg.name}</Text>
                      {pkg.recommended && (
                        <Badge className="recommended-text" text="超值推荐" absolute style={{ top: '-20px', left: '0', marginLeft: '0' }}/>
                      )}
                      
                    </View>
                    <Text className="package-price">
                      {formatPrice(pkg.price)}
                    </Text>
                  </View>
                </View>
                <Button
                  inline
                  size="small"
                  // className={cls("purchase-btn", {
                  //   primary: index === 1,
                  //   outline: index !== 1,
                  // })}
                  type={pkg.recommended ? "primary" : "ghost"}
                  shape="round"
                  onClick={() => handlePurchase(pkg.type)}
                >
                  {isUserVip() ? '续费' : '开通'}
                </Button>
              </View>
            </View>
          ))}
        </View>

        {/* 功能区域 */}
        <View className="functions-section">
          <View className="function-links">
            <Text className="function-link" onClick={goToPurchasedServices}>
              已购服务
            </Text>
            <View className="divider" />
            <Text className="function-link" onClick={restorePurchase}>
              恢复购买
            </Text>
          </View>
          <Button inline size="mini" className="benefits-btn" onClick={goToBenefits}>
            了解会员权益
          </Button>
        </View>

        {/* 底部协议 */}
        <View className="agreement-section">
          <Text className="agreement-text-msg">
            会员付费即代表你同意
          </Text>
          <Text className="agreement-text-link">
            《会员服务协议》
          </Text>
        </View>

        {/* 调试面板 */}
        {isDebugMode() && (
          <DebugPanel
            scenario={getDebugScenario()}
            config={vipCenterDebugger.config}
            onLoadMockData={loadMockData}
            statusInfo={{
              用户: userInfo?.nickname || "未登录",
              头像: userInfo?.avatar || "未登录",
              会员状态: isUserVip() ? "✅ 会员" : "❌ 非会员",
              到期时间: formatExpireTime() || "无",
              套餐数量: VIP_PACKAGES.length,
              支付状态: isPaying ? "⏳ 支付中" : "✅ 空闲",
              支付步骤: paymentStep || "无",
              支付错误: paymentError || "无",
              选中套餐: selectedVipType || "无",
            }}
            customButtons={getPaymentDebugButtons()}
          />
        )}

        {/* 支付方式选择弹窗 */}
        <BottomPopup
          visible={paymentPopupVisible}
          onClose={handlePaymentPopupClose}
          onConfirm={handlePaymentMethodSelect}
          options={['微信支付', '支付宝'] as string[]}
          // title="选择支付方式"
          btnCloseText="取消"
        />
      </View>
    </PageSwipeBack>
  );
};

export default VipCenter;
