@import '@arco-design/mobile-react/style/mixin.less';
@import "../../../utils/css/variables.less";

// 页面根容器样式
[id^="/pages/wallet/transactions/history"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

// 页面主容器
.transactionHistoryPage {
  position: relative;
  width: 100%;
  min-height: 100vh;
  
  // 内容区域
  .content {
    padding: 10px;
  }
  
  // 交易折叠面板
  .transaction-collapse {
    // background-color: var(--page-primary-background-color);
    // .use-dark-mode-query({
    //   background-color: var(--dark-background-color) !important;
    // });
    
    overflow: hidden;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    // 折叠面板项
    .arco-collapse-item {
      border: none;
      border-radius: 4px;
      
    //   &:not(:last-child) {
    //     border-bottom: 0.5px solid var(--line-color);
    //     .use-dark-mode-query({
    //       border-bottom-color: var(--dark-line-color) !important;
    //     });
    //   }
      
      // 折叠面板头部
      .arco-collapse-header {
        margin-left: 0 !important;
        padding: 15px 10px;
        // margin: 15px 10px;
        .use-var(background-color, background-color);
        .use-dark-mode-query({
          background-color: var(--dark-container-background-color) !important;
        });
        
        .collapse-header {
          display: flex;
          flex-direction: column;
          gap: 5px;
          flex: 1;
          
          .date-text {
            font-size: 14px;
            font-weight: 500;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }
          
          .summary-text {
            font-size: 13px;
            .use-var(color, sub-info-font-color);
            .use-dark-mode-query({
              color: var(--dark-sub-info-font-color) !important;
            });
            line-height: 140%;
            white-space: pre-wrap;
          }
        }
        
        // 展开图标
        .arco-collapse-item-icon {
          .use-var(color, sub-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-font-color) !important;
          });
        }

        ::after {
            border-bottom: none !important;
        }
      }
      
      // 折叠面板内容
      .arco-collapse-content {
        padding: 0;
        .use-var(background-color, background-color);
        .use-dark-mode-query({
          background-color: var(--dark-container-background-color) !important;
        });
      }

      .arco-collapse-content-container {
        padding: 0;
      }
    }

    .collapse-icon {
        color: var(--text-4);
        .use-dark-mode-query({
            color: var(--text-2) !important;
        });
    }
  }
  
  // 交易列表
  .transaction-list {
    .transaction-item {
      padding: 15px 0px !important;
      margin-right: 10px;
      border-top: 0.5px solid var(--line-color);
      .use-dark-mode-query({
        border-top-color: var(--dark-line-color) !important;
      });
      
      &:last-child {
        border-bottom: none;
      }
      
      .arco-cell-content {
        padding: 0;
      }
      
      // 交易信息
      .transaction-info {
        display: flex;
        flex-direction: column;
        gap: 5px;
        
        .transaction-title {
          font-size: 14px;
          font-weight: 500;
          .use-var(color, font-color);
          .use-dark-mode-query({
            color: var(--dark-font-color) !important;
          });
        }
        
        .transaction-time {
          font-size: 13px;
          .use-var(color, sub-info-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-info-font-color) !important;
          });
        }
      }
      
      // 交易金额
      .transaction-amount {
        font-size: 14px;
        font-weight: 500;
        
        &.income {
          .use-var(color, success-color);
          .use-dark-mode-query({
            color: var(--dark-success-color) !important;
          });
        }
        
        &.expense {
          .use-var(color, font-color);
          .use-dark-mode-query({
            color: var(--dark-font-color) !important;
          });
        }
      }
      
      // 箭头图标
      .arco-cell-arrow {
        // .use-var(color, sub-font-color);
        color: var(--text-4);
        .use-dark-mode-query({
          color: var(--text-2) !important;
        });
      }
    }
  }
  
  // 空状态
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
    
    text {
      font-size: 15px;
      .use-var(color, sub-info-font-color);
      .use-dark-mode-query({
        color: var(--dark-sub-info-font-color) !important;
      });
    }
  }
  
  // 加载更多组件样式
  .arco-load-more {
    padding: 20px;
    
    .arco-load-more-text {
      .use-var(color, sub-info-font-color);
      .use-dark-mode-query({
        color: var(--dark-sub-info-font-color) !important;
      });
    }
    
    .arco-load-more-loading {
      .arco-loading {
        .use-var(color, primary-color);
        .use-dark-mode-query({
          color: var(--dark-primary-color) !important;
        });
      }
    }
  }
}
